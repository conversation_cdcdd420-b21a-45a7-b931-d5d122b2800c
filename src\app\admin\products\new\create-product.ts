"use server";

import { prisma } from "@/lib/database";
import { z } from "zod"
import { handleActionError } from "@/lib/error-utils";
import { redirect } from "next/navigation";

function getProductWarnings(data: z.infer<typeof productSchema>) {
  const warnings: string[] = []

  if (data.slug.length > 60) {
    warnings.push("Slug is too long and might be truncated in URLs.")
  }

  if (!data.metaDescription || data.metaDescription.length < 50) {
    warnings.push("Meta description is short or missing — this may hurt SEO.")
  }

  if (!data.weight && !data.length && !data.width && !data.heigth) {
    warnings.push("No dimensions provided — this may be needed for shipping.")
  }

  if (!data.tags || data.tags.length === 0) {
    warnings.push("Tags help with internal search. Consider adding some.")
  }

  return warnings
}

const productSchema = z.object({
  name: z.string().min(1, "Name is required."),
  slug: z.string().min(1, "Slug is required."),
  description: z.string().optional(),
  basePrice: z.coerce.number().nonnegative("Price cannot be negative."),
  sku: z.string().min(1, "SKU is required."),
  stock: z.coerce.number().int().nonnegative(),
  isActive: z.boolean().default(true),
  hasOption: z.boolean().default(false),
  weight: z.coerce.number().optional(),
  length: z.coerce.number().optional(),
  width: z.coerce.number().optional(),
  heigth: z.coerce.number().optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  tags: z.array(z.string()).optional(),
  categoryIds: z.array(z.string()).min(1, "At least one category is required."),
})

export async function createProduct(prevState: unknown, formData: FormData) {
    console.log("Hello from the server!");

    const obj = Object.fromEntries(formData.entries());

    const result = productSchema.safeParse(obj);
   if (!result.success) {
        console.log(result.error.flatten());
        return {
            message: "Failed to create a product!",
            error: result.error.message,
        };
    }

    const data = result.data;
    const warnings = getProductWarnings(data)

    try{
        const product = await prisma.product.create({
        data: {
            name: data.name,
            slug: data.slug,
            description: data.description,
            basePrice: data.basePrice,
            sku: data.sku,
            stock: data.stock,
            isActive: data.isActive,
            hasOption: data.hasOption,
            weight: data.weight,
            length: data.length,
            width: data.width,
            heigth: data.heigth,
            metaTitle: data.metaTitle,
            metaDescription: data.metaDescription,
            tags: data.tags,
            categories: {
                connect: data.categoryIds.map((id: string) => ({ id })),
            },
        },
    });
console.log(product);
} catch (error) {
    return handleActionError(error);
    return {
            message: "Failed to create a product!",
            error: "An error occurred while saving to the database.",
                  warnings,

        };
}

redirect("/admin/products");
}