import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface ProductShipmentInfoStepProps {
  fieldErrors?: Record<string, string>;
}

export default function ProductShipmentInfoStep({ fieldErrors }: ProductShipmentInfoStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Shipping Information</CardTitle>
        <CardDescription>Physical dimensions and weight for shipping calculations</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="weight">Weight (kg)</Label>
            <Input
              id="weight"
              name="weight"
              type="number"
              step="0.001"
              min="0"
              placeholder="0.000"
              className={fieldErrors?.weight ? "border-red-500" : ""}
            />
            {fieldErrors?.weight && (
              <p className="text-sm text-red-600">{fieldErrors.weight}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="length">Length (cm)</Label>
            <Input
              id="length"
              name="length"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              className={fieldErrors?.length ? "border-red-500" : ""}
            />
            {fieldErrors?.length && (
              <p className="text-sm text-red-600">{fieldErrors.length}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="width">Width (cm)</Label>
            <Input
              id="width"
              name="width"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              className={fieldErrors?.width ? "border-red-500" : ""}
            />
            {fieldErrors?.width && (
              <p className="text-sm text-red-600">{fieldErrors.width}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="heigth">Height (cm)</Label>
            <Input
              id="heigth"
              name="heigth"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              className={fieldErrors?.heigth ? "border-red-500" : ""}
            />
            {fieldErrors?.heigth && (
              <p className="text-sm text-red-600">{fieldErrors.heigth}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}