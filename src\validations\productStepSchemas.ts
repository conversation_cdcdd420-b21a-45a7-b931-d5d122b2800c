import { z } from "zod";

export const basicInfoSchema = z.object({
  name: z.string().min(1, "Name is required."),
  slug: z.string().min(1, "Slug is required."),
  description: z.string().optional(),
});

export const pricingSchema = z.object({
  basePrice: z.coerce.number().nonnegative("Price cannot be negative."),
  sku: z.string().min(1, "SKU is required."),
  stock: z.coerce.number().int().nonnegative("Stock must be a non-negative number."),
  isActive: z.boolean(),
});

export const categorySchema = z.object({
  categoryIds: z.array(z.string()).min(1, "At least one category is required."),
  tags: z.array(z.string()).optional(),
});

export const optionsSchema = z.object({
  hasOption: z.boolean(),
});

export const shipmentSchema = z.object({
  weight: z.coerce.number().optional(),
  length: z.coerce.number().optional(),
  width: z.coerce.number().optional(),
  heigth: z.coerce.number().optional(),
});

export const metaInfoSchema = z.object({
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
});

// Combined schema for final validation (same as your original)
export const fullProductSchema = z.object({
  name: z.string().min(1, "Name is required."),
  slug: z.string().min(1, "Slug is required."),
  description: z.string().optional(),
  basePrice: z.coerce.number().nonnegative("Price cannot be negative."),
  sku: z.string().min(1, "SKU is required."),
  stock: z.coerce.number().int().nonnegative(),
  isActive: z.boolean().default(true),
  hasOption: z.boolean().default(false),
  weight: z.coerce.number().optional(),
  length: z.coerce.number().optional(),
  width: z.coerce.number().optional(),
  heigth: z.coerce.number().optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  tags: z.array(z.string()).optional(),
  categoryIds: z.array(z.string()).min(1, "At least one category is required."),
});
