"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useTransition } from "react";
import { createProduct } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  basicInfoSchema,
  pricingSchema,
  categorySchema,
  optionsSchema,
  shipmentSchema,
  metaInfoSchema
} from "@/validations/productStepSchemas";

// Step components
import BasicInformationStep from "@/components/admin-product-crud/products/create-product-form-components/basic-information";
import PricingAndStockStep from "@/components/admin-product-crud/products/create-product-form-components/pricing-and-stock";
import CategoriesAndTagsStep from "@/components/admin-product-crud/products/create-product-form-components/categories-and-tags";
import ProductOptionsStep from "@/components/admin-product-crud/products/create-product-form-components/product-options-checkbox";
import ProductShipmentInfoStep from "@/components/admin-product-crud/products/create-product-form-components/product-shipment-info";
import MetaInfoStep from "@/components/admin-product-crud/products/create-product-form-components/meta-info";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

const steps = [
  { label: 'Basic Info' },
  { label: 'Pricing' },
  { label: 'Categories' },
  { label: 'Options' },
  { label: 'Shipping' },
  { label: 'SEO' },
  { label: 'Preview' },
];

// Map steps to their validation schemas
const stepSchemas = [
  basicInfoSchema,      // Step 0: Basic Info
  pricingSchema,        // Step 1: Pricing
  categorySchema,       // Step 2: Categories
  optionsSchema,        // Step 3: Options
  shipmentSchema,       // Step 4: Shipping
  metaInfoSchema,       // Step 5: SEO
  null,                 // Step 6: Preview (no validation needed)
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // Step state
  const [currentStep, setCurrentStep] = useState(0);

  // Form state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    basePrice: "",
    sku: "",
    stock: "",
    weight: "",
    length: "",
    width: "",
    heigth: "",
    metaTitle: "",
    metaDescription: "",
  });

  // Tag handlers
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Form data handlers
  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Normalize step data for validation
  const normalizeStepData = (data: typeof formData, stepIndex: number) => {
    switch (stepIndex) {
      case 0: // Basic Info
        return {
          name: data.name,
          slug: data.slug,
          description: data.description,
        };
      case 1: // Pricing
        return {
          basePrice: data.basePrice ? parseFloat(data.basePrice) : 0,
          sku: data.sku,
          stock: data.stock ? parseInt(data.stock, 10) : 0,
          isActive,
        };
      case 2: // Categories
        return {
          categoryIds: selectedCategories,
          tags,
        };
      case 3: // Options
        return {
          hasOption,
        };
      case 4: // Shipping
        return {
          weight: data.weight ? parseFloat(data.weight) : undefined,
          length: data.length ? parseFloat(data.length) : undefined,
          width: data.width ? parseFloat(data.width) : undefined,
          heigth: data.heigth ? parseFloat(data.heigth) : undefined,
        };
      case 5: // SEO
        return {
          metaTitle: data.metaTitle,
          metaDescription: data.metaDescription,
        };
      default:
        return {};
    }
  };

  // Collect form data from DOM
  const collectFormData = () => {
    const form = document.querySelector('form');
    if (form) {
      const formData = new FormData(form);
      const data: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (typeof value === 'string') {
          data[key] = value;
        }
      }
      setFormData(prev => ({ ...prev, ...data }));
    }
  };

  // Check uniqueness for slug and SKU
  const checkUniqueness = async (field: 'slug' | 'sku', value: string): Promise<{ available: boolean; message: string }> => {
    try {
      const response = await fetch(`/api/products/check-${field}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [field]: value }),
      });
      return await response.json();
    } catch (error) {
      console.error(`Error checking ${field}:`, error);
      return { available: true, message: `Could not verify ${field} availability` };
    }
  };

  // Navigation with async validation
  const nextStep = async () => {
    collectFormData(); // Collect data before validation

    // Skip validation for preview step
    if (currentStep === steps.length - 1) {
      return;
    }

    const schema = stepSchemas[currentStep];

    // Skip validation if no schema for this step
    if (!schema) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1);
      }
      return;
    }

    // Normalize data for current step
    const stepData = normalizeStepData(formData, currentStep);

    // Run basic validation on current step's fields
    const result = schema.safeParse(stepData);

    if (!result.success) {
      const errors: Record<string, string> = {};
      for (const [field, messages] of Object.entries(result.error.flatten().fieldErrors)) {
        if (messages && messages.length > 0) {
          errors[field] = messages[0];
        }
      }
      setFieldErrors(errors);
      setError("Please fix the errors before proceeding.");
      return; // Don't go to next step
    }

    // Additional async validation for specific steps
    const errors: Record<string, string> = {};

    // Step 0: Check slug uniqueness
    if (currentStep === 0 && formData.slug) {
      const slugCheck = await checkUniqueness('slug', formData.slug);
      if (!slugCheck.available) {
        errors.slug = slugCheck.message;
      }
    }

    // Step 1: Check SKU uniqueness
    if (currentStep === 1 && formData.sku) {
      const skuCheck = await checkUniqueness('sku', formData.sku);
      if (!skuCheck.available) {
        errors.sku = skuCheck.message;
      }
    }

    // If there are uniqueness errors, show them and don't proceed
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      setError("Please fix the errors before proceeding.");
      return;
    }

    // Clear errors and proceed to next step
    setFieldErrors({});
    setError("");
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    startTransition(async () => {
      try {
        const submitData = new FormData();
        
        // Add form data
        Object.entries(formData).forEach(([key, value]) => {
          submitData.append(key, value);
        });
        
        // Add other data
        submitData.append('categoryIds', JSON.stringify(selectedCategories));
        submitData.append('tags', JSON.stringify(tags));
        submitData.append('isActive', isActive.toString());
        submitData.append('hasOption', hasOption.toString());

        const result = await createProduct({ message: "", error: "", fieldErrors: {} }, submitData);
        
        if (result.error) {
          setError(result.error);
          setFieldErrors(result.fieldErrors || {});
        } else {
          router.push('/admin/products');
        }
      } catch {
        setError('An unexpected error occurred');
      }
    });
  };

  // Preview component
  const PreviewStep = () => (
    <Card>
      <CardHeader>
        <CardTitle>Product Preview</CardTitle>
        <CardDescription>Review your product before creating</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-2">Basic Information</h3>
            <p><strong>Name:</strong> {formData.name || "Not set"}</p>
            <p><strong>Slug:</strong> {formData.slug || "Not set"}</p>
            <p><strong>Description:</strong> {formData.description || "Not set"}</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Pricing & Stock</h3>
            <p><strong>Price:</strong> {formData.basePrice ? `${formData.basePrice} SEK` : "Not set"}</p>
            <p><strong>SKU:</strong> {formData.sku || "Not set"}</p>
            <p><strong>Stock:</strong> {formData.stock || "Not set"}</p>
            <p><strong>Status:</strong> {isActive ? "Active" : "Inactive"}</p>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {selectedCategories.length > 0 ? (
              selectedCategories.map(catId => {
                const category = categories.find(c => c.id === catId);
                return category ? (
                  <Badge key={catId} variant="secondary">{category.name}</Badge>
                ) : null;
              })
            ) : (
              <span className="text-gray-500">No categories selected</span>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {tags.length > 0 ? (
              tags.map(tag => (
                <Badge key={tag} variant="outline">{tag}</Badge>
              ))
            ) : (
              <span className="text-gray-500">No tags added</span>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Options</h3>
          <p>{hasOption ? "Product has variants/options" : "Simple product (no variants)"}</p>
        </div>

        {(formData.weight || formData.length || formData.width || formData.heigth) && (
          <div>
            <h3 className="font-semibold mb-2">Shipping</h3>
            <p><strong>Weight:</strong> {formData.weight || "0"} kg</p>
            <p><strong>Dimensions:</strong> {formData.length || "0"} × {formData.width || "0"} × {formData.heigth || "0"} cm</p>
          </div>
        )}

        {(formData.metaTitle || formData.metaDescription) && (
          <div>
            <h3 className="font-semibold mb-2">SEO</h3>
            <p><strong>Meta Title:</strong> {formData.metaTitle || "Not set"}</p>
            <p><strong>Meta Description:</strong> {formData.metaDescription || "Not set"}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Simple Progress Header */}
      <div className="bg-white rounded-lg border p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create New Product</h2>
        <p className="text-gray-600 mb-4">Step {currentStep + 1} of {steps.length}: {steps[currentStep].label}</p>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        {/* Messages */}
        {error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4 mb-6">
            <p className="text-red-800">{error}</p>
            {fieldErrors && Object.keys(fieldErrors).length > 0 && (
              <ul className="mt-2 list-disc list-inside text-sm text-red-700">
                {Object.entries(fieldErrors).map(([field, error]) => (
                  <li key={field}>{field}: {error}</li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/* Step Content */}
        <form>
          {currentStep === 0 && (
            <BasicInformationStep
              fieldErrors={fieldErrors}
              formData={formData}
              onFormDataChange={updateFormData}
            />
          )}
          {currentStep === 1 && (
            <PricingAndStockStep
              fieldErrors={fieldErrors}
              isActive={isActive}
              setIsActive={setIsActive}
            />
          )}
          {currentStep === 2 && (
            <CategoriesAndTagsStep
              fieldErrors={fieldErrors}
              categories={categories}
              selectedCategories={selectedCategories}
              setSelectedCategories={setSelectedCategories}
              tags={tags}
              tagInput={tagInput}
              setTagInput={setTagInput}
              handleAddTag={handleAddTag}
              handleRemoveTag={handleRemoveTag}
              handleKeyDown={handleKeyDown}
            />
          )}
          {currentStep === 3 && (
            <ProductOptionsStep
              hasOption={hasOption}
              setHasOption={setHasOption}
            />
          )}
          {currentStep === 4 && <ProductShipmentInfoStep fieldErrors={fieldErrors} />}
          {currentStep === 5 && <MetaInfoStep fieldErrors={fieldErrors} />}
          {currentStep === 6 && <PreviewStep />}
        </form>

        {/* Navigation */}
        <div className="flex justify-between pt-6 border-t mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/products')}
            >
              Cancel
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button onClick={handleSubmit} disabled={isPending}>
                {isPending ? "Creating..." : "Create Product"}
              </Button>
            ) : (
              <Button type="button" onClick={nextStep}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
