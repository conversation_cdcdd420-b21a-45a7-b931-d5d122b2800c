"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useTransition } from "react";
import { createProduct } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Step components
import BasicInformationStep from "@/components/admin-product-crud/products/create-product-form-components/basic-information";
import PricingAndStockStep from "@/components/admin-product-crud/products/create-product-form-components/pricing-and-stock";
import CategoriesAndTagsStep from "@/components/admin-product-crud/products/create-product-form-components/categories-and-tags";
import ProductOptionsStep from "@/components/admin-product-crud/products/create-product-form-components/product-options-checkbox";
import ProductShipmentInfoStep from "@/components/admin-product-crud/products/create-product-form-components/product-shipment-info";
import MetaInfoStep from "@/components/admin-product-crud/products/create-product-form-components/meta-info";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

const steps = [
  { label: 'Basic Info' },
  { label: 'Pricing' },
  { label: 'Categories' },
  { label: 'Options' },
  { label: 'Shipping' },
  { label: 'SEO' },
  { label: 'Preview' },
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string>("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // Step state
  const [currentStep, setCurrentStep] = useState(0);

  // Form state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    basePrice: "",
    sku: "",
    stock: "",
    weight: "",
    length: "",
    width: "",
    heigth: "",
    metaTitle: "",
    metaDescription: "",
  });

  // Tag handlers
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Form data handlers
  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Collect form data from DOM
  const collectFormData = () => {
    const form = document.querySelector('form');
    if (form) {
      const formData = new FormData(form);
      const data: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (typeof value === 'string') {
          data[key] = value;
        }
      }
      setFormData(prev => ({ ...prev, ...data }));
    }
  };

  // Navigation
  const nextStep = () => {
    collectFormData(); // Collect data before moving to next step
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    startTransition(async () => {
      try {
        const submitData = new FormData();
        
        // Add form data
        Object.entries(formData).forEach(([key, value]) => {
          submitData.append(key, value);
        });
        
        // Add other data
        submitData.append('categoryIds', JSON.stringify(selectedCategories));
        submitData.append('tags', JSON.stringify(tags));
        submitData.append('isActive', isActive.toString());
        submitData.append('hasOption', hasOption.toString());

        const result = await createProduct({ message: "", error: "", fieldErrors: {} }, submitData);
        
        if (result.error) {
          setError(result.error);
          setFieldErrors(result.fieldErrors || {});
        } else {
          router.push('/admin/products');
        }
      } catch {
        setError('An unexpected error occurred');
      }
    });
  };

  // Preview component
  const PreviewStep = () => (
    <Card>
      <CardHeader>
        <CardTitle>Product Preview</CardTitle>
        <CardDescription>Review your product before creating</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-2">Basic Information</h3>
            <p><strong>Name:</strong> {formData.name || "Not set"}</p>
            <p><strong>Slug:</strong> {formData.slug || "Not set"}</p>
            <p><strong>Description:</strong> {formData.description || "Not set"}</p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Pricing & Stock</h3>
            <p><strong>Price:</strong> {formData.basePrice ? `${formData.basePrice} SEK` : "Not set"}</p>
            <p><strong>SKU:</strong> {formData.sku || "Not set"}</p>
            <p><strong>Stock:</strong> {formData.stock || "Not set"}</p>
            <p><strong>Status:</strong> {isActive ? "Active" : "Inactive"}</p>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {selectedCategories.length > 0 ? (
              selectedCategories.map(catId => {
                const category = categories.find(c => c.id === catId);
                return category ? (
                  <Badge key={catId} variant="secondary">{category.name}</Badge>
                ) : null;
              })
            ) : (
              <span className="text-gray-500">No categories selected</span>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {tags.length > 0 ? (
              tags.map(tag => (
                <Badge key={tag} variant="outline">{tag}</Badge>
              ))
            ) : (
              <span className="text-gray-500">No tags added</span>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Options</h3>
          <p>{hasOption ? "Product has variants/options" : "Simple product (no variants)"}</p>
        </div>

        {(formData.weight || formData.length || formData.width || formData.heigth) && (
          <div>
            <h3 className="font-semibold mb-2">Shipping</h3>
            <p><strong>Weight:</strong> {formData.weight || "0"} kg</p>
            <p><strong>Dimensions:</strong> {formData.length || "0"} × {formData.width || "0"} × {formData.heigth || "0"} cm</p>
          </div>
        )}

        {(formData.metaTitle || formData.metaDescription) && (
          <div>
            <h3 className="font-semibold mb-2">SEO</h3>
            <p><strong>Meta Title:</strong> {formData.metaTitle || "Not set"}</p>
            <p><strong>Meta Description:</strong> {formData.metaDescription || "Not set"}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Simple Progress Header */}
      <div className="bg-white rounded-lg border p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create New Product</h2>
        <p className="text-gray-600 mb-4">Step {currentStep + 1} of {steps.length}: {steps[currentStep].label}</p>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        {/* Messages */}
        {error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4 mb-6">
            <p className="text-red-800">{error}</p>
            {fieldErrors && Object.keys(fieldErrors).length > 0 && (
              <ul className="mt-2 list-disc list-inside text-sm text-red-700">
                {Object.entries(fieldErrors).map(([field, error]) => (
                  <li key={field}>{field}: {error}</li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/* Step Content */}
        <form>
          {currentStep === 0 && <BasicInformationStep fieldErrors={fieldErrors} />}
          {currentStep === 1 && (
            <PricingAndStockStep
              fieldErrors={fieldErrors}
              isActive={isActive}
              setIsActive={setIsActive}
            />
          )}
          {currentStep === 2 && (
            <CategoriesAndTagsStep
              fieldErrors={fieldErrors}
              categories={categories}
              selectedCategories={selectedCategories}
              setSelectedCategories={setSelectedCategories}
              tags={tags}
              tagInput={tagInput}
              setTagInput={setTagInput}
              handleAddTag={handleAddTag}
              handleRemoveTag={handleRemoveTag}
              handleKeyDown={handleKeyDown}
            />
          )}
          {currentStep === 3 && (
            <ProductOptionsStep
              hasOption={hasOption}
              setHasOption={setHasOption}
            />
          )}
          {currentStep === 4 && <ProductShipmentInfoStep fieldErrors={fieldErrors} />}
          {currentStep === 5 && <MetaInfoStep fieldErrors={fieldErrors} />}
          {currentStep === 6 && <PreviewStep />}
        </form>

        {/* Navigation */}
        <div className="flex justify-between pt-6 border-t mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/products')}
            >
              Cancel
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button onClick={handleSubmit} disabled={isPending}>
                {isPending ? "Creating..." : "Create Product"}
              </Button>
            ) : (
              <Button type="button" onClick={nextStep}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
