"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useActionState, useState } from "react";
import { createProduct, type ActionState } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { ChevronLeft, ChevronRight } from "lucide-react";

// Step components
import BasicInformationStep from "@/components/admin-product-crud/products/create-product-form-components/basic-information";
import PricingAndStockStep from "@/components/admin-product-crud/products/create-product-form-components/pricing-and-stock";
import CategoriesAndTagsStep from "@/components/admin-product-crud/products/create-product-form-components/categories-and-tags";
import ProductOptionsStep from "@/components/admin-product-crud/products/create-product-form-components/product-options-checkbox";
import ProductShipmentInfoStep from "@/components/admin-product-crud/products/create-product-form-components/product-shipment-info";
import MetaInfoStep from "@/components/admin-product-crud/products/create-product-form-components/meta-info";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

const steps = [
  { label: 'Basic Info' },
  { label: 'Pricing' },
  { label: 'Categories' },
  { label: 'Options' },
  { label: 'Shipping' },
  { label: 'SEO' },
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createProduct, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  // Step state
  const [currentStep, setCurrentStep] = useState(0);

  // Form state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);



  // Tag handlers
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Navigation
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Render all steps but hide inactive ones
  const renderAllSteps = () => {
    return (
      <>
        <div style={{ display: currentStep === 0 ? 'block' : 'none' }}>
          <BasicInformationStep fieldErrors={state.fieldErrors} />
        </div>
        <div style={{ display: currentStep === 1 ? 'block' : 'none' }}>
          <PricingAndStockStep
            fieldErrors={state.fieldErrors}
            isActive={isActive}
            setIsActive={setIsActive}
          />
        </div>
        <div style={{ display: currentStep === 2 ? 'block' : 'none' }}>
          <CategoriesAndTagsStep
            fieldErrors={state.fieldErrors}
            categories={categories}
            selectedCategories={selectedCategories}
            setSelectedCategories={setSelectedCategories}
            tags={tags}
            tagInput={tagInput}
            setTagInput={setTagInput}
            handleAddTag={handleAddTag}
            handleRemoveTag={handleRemoveTag}
            handleKeyDown={handleKeyDown}
          />
        </div>
        <div style={{ display: currentStep === 3 ? 'block' : 'none' }}>
          <ProductOptionsStep
            hasOption={hasOption}
            setHasOption={setHasOption}
          />
        </div>
        <div style={{ display: currentStep === 4 ? 'block' : 'none' }}>
          <ProductShipmentInfoStep fieldErrors={state.fieldErrors} />
        </div>
        <div style={{ display: currentStep === 5 ? 'block' : 'none' }}>
          <MetaInfoStep fieldErrors={state.fieldErrors} />
        </div>
      </>
    );
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Simple Progress Header */}
      <div className="bg-white rounded-lg border p-6 shadow-sm">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Create New Product</h2>
        <p className="text-gray-600 mb-4">Step {currentStep + 1} of {steps.length}: {steps[currentStep].label}</p>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <form action={formAction} className="space-y-6">
          {/* Hidden inputs */}
          <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
          <input type="hidden" name="tags" value={JSON.stringify(tags)} />
          <input type="hidden" name="isActive" value={isActive.toString()} />
          <input type="hidden" name="hasOption" value={hasOption.toString()} />

          {/* Messages */}
          {state.message && (
            <div className="rounded-md border border-green-300 bg-green-50 p-4">
              <p className="text-green-800">{state.message}</p>
            </div>
          )}
          
          {state.error && (
            <div className="rounded-md border border-red-300 bg-red-50 p-4">
              <p className="text-red-800">{state.error}</p>
              {state.fieldErrors && Object.keys(state.fieldErrors).length > 0 && (
                <ul className="mt-2 list-disc list-inside text-sm text-red-700">
                  {Object.entries(state.fieldErrors).map(([field, error]) => (
                    <li key={field}>{field}: {error}</li>
                  ))}
                </ul>
              )}
            </div>
          )}

          {/* Step Content */}
          {renderStepContent()}

          {/* Navigation */}
          <div className="flex justify-between pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/products')}
              >
                Cancel
              </Button>

              {currentStep === steps.length - 1 ? (
                <Button type="submit" disabled={isPending}>
                  {isPending ? "Creating..." : "Create Product"}
                </Button>
              ) : (
                <Button type="button" onClick={nextStep}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
