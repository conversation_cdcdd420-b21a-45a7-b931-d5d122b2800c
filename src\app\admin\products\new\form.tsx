"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useActionState, useState } from "react";
import { createProduct, type ActionState } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { Stepper } from 'react-form-stepper';
import { ChevronLeft, ChevronRight, CheckCircle } from "lucide-react";

// modularized step components
import BasicInformationStep from "@/components/admin-product-crud/products/create-product-form-components/basic-information";
import PricingAndStockStep from "@/components/admin-product-crud/products/create-product-form-components/pricing-and-stock";
import CategoriesAndTagsStep from "@/components/admin-product-crud/products/create-product-form-components/categories-and-tags";
import ProductOptionsStep from "@/components/admin-product-crud/products/create-product-form-components/product-options-checkbox";
import ProductShipmentInfoStep from "@/components/admin-product-crud/products/create-product-form-components/product-shipment-info";
import MetaInfoStep from "@/components/admin-product-crud/products/create-product-form-components/meta-info";


interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

const steps = [
  { label: 'Basic Info', description: 'Product name, slug, and description' },
  { label: 'Pricing', description: 'Price, SKU, stock, and status' },
  { label: 'Categories', description: 'Product categories and tags' },
  { label: 'Options', description: 'Variants and product options' },
  { label: 'Shipping', description: 'Dimensions and weight' },
  { label: 'SEO', description: 'Meta title and description' },
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createProduct, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  // stepping state
  const [currentStep, setCurrentStep] = useState(0);

  // interactive elements state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);

  // functions to handle interactive elements
  const handleAddTag = () => {
  if (tagInput.trim() && !tags.includes(tagInput.trim())) {
    setTags([...tags, tagInput.trim()]);
    setTagInput("");
  }
};

const handleRemoveTag = (tagToRemove: string) => {
  setTags(tags.filter(tag => tag !== tagToRemove));
};
 const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // step validation
  const isStepValid = (step: number) => {
    switch (step) {
      case 0: // Basic Info
        return true; // Always allow navigation from basic info
      case 1: // Pricing
        return true; // Always allow navigation from pricing
      case 2: // Categories
        return selectedCategories.length > 0; // Require at least one category
      case 3: // Options
        return true; // Always allow navigation from options
      case 4: // Shipping
        return true; // Always allow navigation from shipping
      case 5: // SEO
        return true; // Always allow navigation from SEO
      default:
        return true;
    }
  };

  // step navigation
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      if (isStepValid(currentStep)) {
        setCurrentStep(currentStep + 1);
      } else {
        // Could add toast notification here for validation errors
        console.log('Please complete required fields before proceeding');
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <BasicInformationStep fieldErrors={state.fieldErrors} />;
      case 1:
        return (
          <PricingAndStockStep
            fieldErrors={state.fieldErrors}
            isActive={isActive}
            setIsActive={setIsActive}
          />
        );
      case 2:
        return (
          <CategoriesAndTagsStep
            fieldErrors={state.fieldErrors}
            categories={categories}
            selectedCategories={selectedCategories}
            setSelectedCategories={setSelectedCategories}
            tags={tags}
            tagInput={tagInput}
            setTagInput={setTagInput}
            handleAddTag={handleAddTag}
            handleRemoveTag={handleRemoveTag}
            handleKeyDown={handleKeyDown}
          />
        );
      case 3:
        return (
          <ProductOptionsStep
            hasOption={hasOption}
            setHasOption={setHasOption}
          />
        );
      case 4:
        return <ProductShipmentInfoStep fieldErrors={state.fieldErrors} />;
      case 5:
        return <MetaInfoStep fieldErrors={state.fieldErrors} />;
      default:
        return <BasicInformationStep fieldErrors={state.fieldErrors} />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header with Progress */}
      <div className="bg-white rounded-lg border p-6 shadow-sm">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Create New Product</h2>
          <div className="space-y-1">
            <p className="text-lg font-medium text-gray-800">
              Step {currentStep + 1}: {steps[currentStep].label}
            </p>
            <p className="text-gray-600">{steps[currentStep].description}</p>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between items-center text-xs text-gray-500 mb-2">
              <span>Progress</span>
              <div className="flex items-center space-x-2">
                {currentStep === steps.length - 1 && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
                <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Enhanced Stepper */}
        <Stepper
          steps={steps}
          activeStep={currentStep}
          styleConfig={{
            activeBgColor: '#2563eb',
            completedBgColor: '#059669',
            inactiveBgColor: '#f3f4f6',
            activeTextColor: '#ffffff',
            completedTextColor: '#ffffff',
            inactiveTextColor: '#9ca3af',
            size: '2.5em',
            circleFontSize: '1rem',
            labelFontSize: '0.875rem',
            borderRadius: '50%',
            fontWeight: 600,
          }}
        />
      </div>

      {/* Main Form Content */}
      <div className="bg-white rounded-lg border shadow-sm">
        <form action={formAction} className="p-6 space-y-6">
          {/* Hidden inputs */}
          <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
          <input type="hidden" name="tags" value={JSON.stringify(tags)} />
          <input type="hidden" name="isActive" value={isActive.toString()} />
          <input type="hidden" name="hasOption" value={hasOption.toString()} />

        {/* Error and Success Messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}

        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        {Array.isArray(state.warnings) && state.warnings.length > 0 && (
          <div className="rounded-md border border-yellow-300 bg-yellow-50 p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">Warnings</h4>
            <ul className="list-disc list-inside text-yellow-700 text-sm space-y-1">
              {state.warnings.map((warning, i) => (
                <li key={i}>{warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Enhanced Navigation */}
        <div className="border-t pt-6 mt-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0}
                className="flex items-center"
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              {currentStep > 0 && (
                <span className="text-sm text-gray-500">
                  You can go back to edit previous steps
                </span>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="ghost"
                onClick={() => router.push('/admin/products')}
                className="text-gray-600 hover:text-gray-800"
              >
                Cancel
              </Button>

              {currentStep === steps.length - 1 ? (
                <Button
                  type="submit"
                  disabled={isPending}
                  className="bg-green-600 hover:bg-green-700 text-white px-6"
                >
                  {isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Product...
                    </>
                  ) : (
                    "Create Product"
                  )}
                </Button>
              ) : (
                <div className="flex flex-col items-end">
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={!isStepValid(currentStep)}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6"
                  >
                    Continue to {steps[currentStep + 1]?.label}
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                  {!isStepValid(currentStep) && currentStep === 2 && (
                    <span className="text-xs text-red-500 mt-1">
                      Please select at least one category
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        </form>
      </div>
    </div>
  );
}
