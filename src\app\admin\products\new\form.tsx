"use client";


import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button";
import { createProduct } from "@/lib/actions/products/create-product";
import { useActionState } from "react";



export default function CreateProductForm() {
const [state, formAction, isPending]=useActionState(createProduct, {
    message: " ",
    error: " "
});
    return (
        <form action={formAction} >

  {state.message && <p className="text-green-600">{state.message}</p>}
{state.error && <p className="text-red-600">{state.error}</p>}

{Array.isArray(state.warnings) && state.warnings.length > 0 && (
  <div className="rounded-md border border-yellow-300 bg-yellow-50 p-4 my-4">
    <h4 className="font-semibold text-yellow-800 mb-2">Warnings</h4>
    <ul className="list-disc list-inside text-yellow-700 text-sm space-y-1">
      {state.warnings.map((warning, i) => (
        <li key={i}>{warning}</li>
      ))}
    </ul>
  </div>
)}



  <div>
    <Label htmlFor="name">Name</Label>
    <Input id="name" name="name" required />
    </div>
  <div>
    <Label htmlFor="slug">Slug</Label>
    <Input id="slug" name="slug" required />
  </div>
  <div>
    <Label htmlFor="description">Description</Label>
    <Textarea id="description" name="description" />
  </div>
  <div>
    <Label htmlFor="basePrice">Base Price</Label>
    <Input id="basePrice" name="basePrice" type="number" required />
  </div>
  <div>
    <Label htmlFor="sku">SKU</Label>
    <Input id="sku" name="sku" required />
  </div>
  <div>
    <Label htmlFor="stock">Stock</Label>
    <Input id="stock" name="stock" type="number" required />
  </div>
  <div>
    <Label htmlFor="isActive">Is Active</Label>
    <Input id="isActive" name="isActive" type="checkbox" />
  </div>
  <div>
    <Label htmlFor="hasOption">Has Option</Label>
    <Input id="hasOption" name="hasOption" type="checkbox" />
  </div>
  <div>
    <Label htmlFor="weight">Weight</Label>
    <Input id="weight" name="weight" type="number" />
  </div>
  <div>
    <Label htmlFor="length">Length</Label>
    <Input id="length" name="length" type="number" />
  </div>
  <div>
    <Label htmlFor="width">Width</Label>
    <Input id="width" name="width" type="number" />
  </div>
  <div>
    <Label htmlFor="heigth">Heigth</Label>
    <Input id="heigth" name="heigth" type="number" />
  </div>
  <div>
    <Label htmlFor="metaTitle">Meta Title</Label>
    <Input id="metaTitle" name="metaTitle" />
  </div>
  <div>
    <Label htmlFor="metaDescription">Meta Description</Label>
    <Textarea id="metaDescription" name="metaDescription" />
  </div>
  <div>
    <Label htmlFor="tags">Tags</Label>
    <Input id="tags" name="tags" />
  </div>
  <div>
    <Label htmlFor="categoryIds">Category IDs</Label>
    <Input id="categoryIds" name="categoryIds" />
  </div>



                <Button type="submit" disabled={isPending}>
                    {isPending?"Submitting": "Submit"}
                </Button>
           
                        </form>
    );
}