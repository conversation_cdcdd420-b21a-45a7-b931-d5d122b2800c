"use client";

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { createProduct, type ActionState } from "@/app/admin/products/new/create-product";
import { useActionState, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { X, ChevronLeft, ChevronRight } from "lucide-react";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

type FormStep = 'basic' | 'pricing' | 'categories' | 'options' | 'shipping' | 'seo';

const STEPS: { key: FormStep; title: string; description: string }[] = [
  { key: 'basic', title: 'Basic Information', description: 'Product name, description, and slug' },
  { key: 'pricing', title: 'Pricing & Inventory', description: 'Price, SKU, and stock' },
  { key: 'categories', title: 'Categories & Tags', description: 'Organize your product' },
  { key: 'options', title: 'Product Options', description: 'Variants and options' },
  { key: 'shipping', title: 'Shipping Info', description: 'Dimensions and weight' },
  { key: 'seo', title: 'SEO & Meta', description: 'Search optimization' },
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createProduct, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  // Form state
  const [currentStep, setCurrentStep] = useState<FormStep>('basic');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);

  // Auto-generate slug from name
  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");

  const generateSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[åä]/g, 'a')
      .replace(/ö/g, 'o')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  useEffect(() => {
    if (name && !slug) {
      setSlug(generateSlug(name));
    }
  }, [name, slug]);

  // Step navigation
  const currentStepIndex = STEPS.findIndex(step => step.key === currentStep);
  const progress = ((currentStepIndex + 1) / STEPS.length) * 100;

  const nextStep = () => {
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < STEPS.length) {
      setCurrentStep(STEPS[nextIndex].key);
    }
  };

  const prevStep = () => {
    const prevIndex = currentStepIndex - 1;
    if (prevIndex >= 0) {
      setCurrentStep(STEPS[prevIndex].key);
    }
  };

  // Tag and category handlers
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'basic':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>Essential product details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                    className={state.fieldErrors?.name ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.name && (
                    <p className="text-sm text-red-600">{state.fieldErrors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={slug}
                    onChange={(e) => setSlug(e.target.value)}
                    required
                    className={state.fieldErrors?.slug ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.slug && (
                    <p className="text-sm text-red-600">{state.fieldErrors.slug}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  rows={4}
                  placeholder="Detailed product description..."
                  className={state.fieldErrors?.description ? "border-red-500" : ""}
                />
                {state.fieldErrors?.description && (
                  <p className="text-sm text-red-600">{state.fieldErrors.description}</p>
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 'pricing':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Pricing & Inventory</CardTitle>
              <CardDescription>Price, stock, and product codes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="basePrice">Base Price (SEK) *</Label>
                  <Input
                    id="basePrice"
                    name="basePrice"
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    className={state.fieldErrors?.basePrice ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.basePrice && (
                    <p className="text-sm text-red-600">{state.fieldErrors.basePrice}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sku">SKU *</Label>
                  <Input
                    id="sku"
                    name="sku"
                    required
                    placeholder="e.g., PROD-001"
                    className={state.fieldErrors?.sku ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.sku && (
                    <p className="text-sm text-red-600">{state.fieldErrors.sku}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stock">Stock Quantity *</Label>
                  <Input
                    id="stock"
                    name="stock"
                    type="number"
                    min="0"
                    required
                    className={state.fieldErrors?.stock ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.stock && (
                    <p className="text-sm text-red-600">{state.fieldErrors.stock}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                  <Label htmlFor="isActive">Product is active (visible to customers)</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'categories':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Categories & Tags</CardTitle>
              <CardDescription>Organize your product</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Categories *</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                  {categories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={selectedCategories.includes(category.id)}
                        onCheckedChange={() => handleCategoryChange(category.id)}
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {category.name}
                      </Label>
                    </div>
                  ))}
                </div>
                {state.fieldErrors?.categoryIds && (
                  <p className="text-sm text-red-600">{state.fieldErrors.categoryIds}</p>
                )}
                {selectedCategories.length === 0 && (
                  <p className="text-sm text-gray-500">Select at least one category</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tagInput">Tags</Label>
                <div className="flex gap-2">
                  <Input
                    id="tagInput"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Add tags (press Enter)"
                  />
                  <Button type="button" onClick={handleAddTag} variant="outline">
                    Add
                  </Button>
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => handleRemoveTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 'options':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Product Options</CardTitle>
              <CardDescription>Configure variants and options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasOption"
                  checked={hasOption}
                  onCheckedChange={setHasOption}
                />
                <Label htmlFor="hasOption">This product has variants/options</Label>
              </div>

              {hasOption && (
                <div className="p-4 border rounded-lg bg-blue-50">
                  <p className="text-sm text-blue-800 mb-2">
                    <strong>Note:</strong> Variant management will be available after creating the base product.
                  </p>
                  <p className="text-sm text-blue-700">
                    You'll be able to add options like size, color, etc., and create specific variants with their own pricing and inventory.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        );

      case 'shipping':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
              <CardDescription>Physical dimensions and weight for shipping calculations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    name="weight"
                    type="number"
                    step="0.001"
                    min="0"
                    placeholder="0.000"
                    className={state.fieldErrors?.weight ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.weight && (
                    <p className="text-sm text-red-600">{state.fieldErrors.weight}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="length">Length (cm)</Label>
                  <Input
                    id="length"
                    name="length"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className={state.fieldErrors?.length ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.length && (
                    <p className="text-sm text-red-600">{state.fieldErrors.length}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="width">Width (cm)</Label>
                  <Input
                    id="width"
                    name="width"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className={state.fieldErrors?.width ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.width && (
                    <p className="text-sm text-red-600">{state.fieldErrors.width}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="heigth">Height (cm)</Label>
                  <Input
                    id="heigth"
                    name="heigth"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    className={state.fieldErrors?.heigth ? "border-red-500" : ""}
                  />
                  {state.fieldErrors?.heigth && (
                    <p className="text-sm text-red-600">{state.fieldErrors.heigth}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'seo':
        return (
          <Card>
            <CardHeader>
              <CardTitle>SEO & Meta Information</CardTitle>
              <CardDescription>Search engine optimization settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="metaTitle">Meta Title</Label>
                <Input
                  id="metaTitle"
                  name="metaTitle"
                  placeholder="SEO title for search engines"
                  className={state.fieldErrors?.metaTitle ? "border-red-500" : ""}
                />
                {state.fieldErrors?.metaTitle && (
                  <p className="text-sm text-red-600">{state.fieldErrors.metaTitle}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="metaDescription">Meta Description</Label>
                <Textarea
                  id="metaDescription"
                  name="metaDescription"
                  rows={3}
                  placeholder="Brief description for search engine results (150-160 characters recommended)"
                  className={state.fieldErrors?.metaDescription ? "border-red-500" : ""}
                />
                {state.fieldErrors?.metaDescription && (
                  <p className="text-sm text-red-600">{state.fieldErrors.metaDescription}</p>
                )}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">{STEPS[currentStepIndex].title}</h2>
            <p className="text-gray-600">{STEPS[currentStepIndex].description}</p>
          </div>
          <div className="text-sm text-gray-500">
            Step {currentStepIndex + 1} of {STEPS.length}
          </div>
        </div>

        <Progress value={progress} className="w-full" />

        {/* Step indicators */}
        <div className="flex justify-between">
          {STEPS.map((step, index) => (
            <div
              key={step.key}
              className={`flex items-center space-x-2 ${
                index <= currentStepIndex ? 'text-blue-600' : 'text-gray-400'
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index < currentStepIndex
                  ? 'bg-blue-600 text-white'
                  : index === currentStepIndex
                    ? 'bg-blue-100 text-blue-600 border-2 border-blue-600'
                    : 'bg-gray-200 text-gray-400'
              }`}>
                {index + 1}
              </div>
              <span className="hidden sm:block text-sm font-medium">{step.title}</span>
            </div>
          ))}
        </div>
      </div>

      <form action={formAction} className="space-y-6">
        {/* Hidden inputs for arrays and booleans */}
        <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
        <input type="hidden" name="tags" value={JSON.stringify(tags)} />
        <input type="hidden" name="isActive" value={isActive.toString()} />
        <input type="hidden" name="hasOption" value={hasOption.toString()} />

        {/* Error and Success Messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}

        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        {Array.isArray(state.warnings) && state.warnings.length > 0 && (
          <div className="rounded-md border border-yellow-300 bg-yellow-50 p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">Warnings</h4>
            <ul className="list-disc list-inside text-yellow-700 text-sm space-y-1">
              {state.warnings.map((warning, i) => (
                <li key={i}>{warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStepIndex === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/products')}
            >
              Cancel
            </Button>

            {currentStepIndex === STEPS.length - 1 ? (
              <Button type="submit" disabled={isPending}>
                {isPending ? "Creating Product..." : "Create Product"}
              </Button>
            ) : (
              <Button type="button" onClick={nextStep}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}