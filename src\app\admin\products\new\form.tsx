"use client";


import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button";
import { useActionState, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { createProduct, type ActionState } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { Stepper } from 'react-form-stepper';


interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}


export default function CreateProductForm({categories}:CreateProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending]=useActionState(createProduct, {
    message: " ",
    error: " ",
    fieldErrors:{},
} as ActionState);



//interactive elemetns
 const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
 const [tags, setTags] = useState<string[]>([]);
 const [tagInput, setTagInput] = useState("");
 const [isActive, setIsActive] = useState(true);
 const [hasOption, setHasOption] = useState(false);


 // functions to handle itneractive elements
 
 //tags
const handleAddTag = () => {
  if (tagInput.trim() && !tags.includes(tagInput.trim())) {
    setTags([...tags, tagInput.trim()]);
    setTagInput("");
  }
};

const handleRemoveTag = (tagToRemove: string) => {
  setTags(tags.filter(tag => tag !== tagToRemove));
};
 const handleKeyDown = (e: React.KeyboardEvent) => {
   if (e.key === 'Enter') {
     e.preventDefault();
     handleAddTag();
   }
 };


 // categories
   const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

    return (
        <form action={formAction} className=" max-w-2xl mx-auto space-y-6" >
{/* hidden inputs */}
<Input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
<Input type="hidden" name="tags" value={JSON.stringify(tags)} />
<Input type="hidden" name="isActive" value={isActive.toString()} />
<Input type="hidden" name="hasOption" value={hasOption.toString()} />


{/* errors/success msgs */}
  {state.message && <p className="text-green-600">{state.message}</p>}
{state.error && <p className="text-red-600">{state.error}</p>}

{Array.isArray(state.warnings) && state.warnings.length > 0 && (
  <div className="rounded-md border border-yellow-300 bg-yellow-50 p-4 my-4">
    <h4 className="font-semibold text-yellow-800 mb-2">Warnings</h4>
    <ul className="list-disc list-inside text-yellow-700 text-sm space-y-1">
      {state.warnings.map((warning, i) => (
        <li key={i}>{warning}</li>
      ))}
    </ul>
  </div>
)}

{/* description, slug and name */}

<Card>
  <CardHeader>
    <CardTitle>Basic Information</CardTitle>
    <CardDescription>Essential product details</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="name">Product Name *</Label>
        <Input
          id="name"
          name="name"
          required
          className={state.fieldErrors?.name ? "border-red-500" : ""}
        />
        {state.fieldErrors?.name && (
          <p className="text-sm text-red-600">{state.fieldErrors.name}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="slug">URL Slug *</Label>
        <Input
          id="slug"
          name="slug"
          required
          className={state.fieldErrors?.slug ? "border-red-500" : ""}
        />
        {state.fieldErrors?.slug && (
          <p className="text-sm text-red-600">{state.fieldErrors.slug}</p>
        )}
      </div>
    </div>

    <div className="space-y-2">
      <Label htmlFor="description">Description</Label>
      <Textarea
        id="description"
        name="description"
        rows={4}
        placeholder="Detailed product description..."
        className={state.fieldErrors?.description ? "border-red-500" : ""}
      />
      {state.fieldErrors?.description && (
        <p className="text-sm text-red-600">{state.fieldErrors.description}</p>
      )}
    </div>
  </CardContent>
</Card>

{/* price, stock, sku, isActive */}
 <Card>
   <CardHeader>
     <CardTitle>Pricing & Inventory</CardTitle>
     <CardDescription>Price, stock, and product codes</CardDescription>
   </CardHeader>
   <CardContent className="space-y-4">
     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
       <div className="space-y-2">
         <Label htmlFor="basePrice">Base Price (SEK) *</Label>
         <Input
           id="basePrice"
           name="basePrice"
           type="number"
           step="0.01"
           min="0"
           required
           className={state.fieldErrors?.basePrice ? "border-red-500" : ""}
         />
         {state.fieldErrors?.basePrice && (
           <p className="text-sm text-red-600">{state.fieldErrors.basePrice}</p>
         )}
       </div>

       <div className="space-y-2">
         <Label htmlFor="sku">SKU *</Label>
         <Input
           id="sku"
           name="sku"
           required
           placeholder="e.g., PROD-001"
           className={state.fieldErrors?.sku ? "border-red-500" : ""}
         />
         {state.fieldErrors?.sku && (
           <p className="text-sm text-red-600">{state.fieldErrors.sku}</p>
         )}
       </div>

       <div className="space-y-2">
         <Label htmlFor="stock">Stock Quantity *</Label>
         <Input
           id="stock"
           name="stock"
           type="number"
           min="0"
           required
           className={state.fieldErrors?.stock ? "border-red-500" : ""}
         />
         {state.fieldErrors?.stock && (
           <p className="text-sm text-red-600">{state.fieldErrors.stock}</p>
         )}
       </div>
     </div>

     <div className="flex items-center space-x-2">
       <Checkbox
         id="isActive"
         checked={isActive}
         onCheckedChange={(checked) => setIsActive(checked === true)}
       />
       <Label htmlFor="isActive">Product is active (visible to customers)</Label>
     </div>
   </CardContent>
 </Card>

{/* categories, tags */}

 <Card>
   <CardHeader>
     <CardTitle>Categories & Tags</CardTitle>
     <CardDescription>Organize your product</CardDescription>
   </CardHeader>
   <CardContent className="space-y-4">
     <div className="space-y-2">
       <Label>Categories *</Label>
       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
         {categories.map((category) => (
           <div key={category.id} className="flex items-center space-x-2">
             <Checkbox
               id={`category-${category.id}`}
               checked={selectedCategories.includes(category.id)}
               onCheckedChange={() => handleCategoryChange(category.id)}
             />
             <Label
               htmlFor={`category-${category.id}`}
               className="text-sm font-normal cursor-pointer"
             >
               {category.name}
             </Label>
           </div>
         ))}
       </div>
       {state.fieldErrors?.categoryIds && (
         <p className="text-sm text-red-600">{state.fieldErrors.categoryIds}</p>
       )}
       {selectedCategories.length === 0 && (
         <p className="text-sm text-gray-500">Select at least one category</p>
       )}
     </div>

     <div className="space-y-2">
       <Label htmlFor="tagInput">Tags</Label>
       <div className="flex gap-2">
         <Input
           id="tagInput"
           value={tagInput}
           onChange={(e) => setTagInput(e.target.value)}
           onKeyDown={handleKeyDown}
           placeholder="Add tags (press Enter)"
         />
         <Button type="button" onClick={handleAddTag} variant="outline">
           Add
         </Button>
       </div>
       {tags.length > 0 && (
         <div className="flex flex-wrap gap-2 mt-2">
           {tags.map((tag, index) => (
             <Badge key={index} variant="secondary" className="flex items-center gap-1">
               {tag}
               <X
                 className="h-3 w-3 cursor-pointer"
                 onClick={() => handleRemoveTag(tag)}
               />
             </Badge>
           ))}
         </div>
       )}
     </div>
   </CardContent>
 </Card>

{/* product options (if has options configure later) if no then procceed with basic product*/}
<Card>
  <CardHeader>
    <CardTitle>Product Options</CardTitle>
    <CardDescription>Configure variants and options</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="flex items-center space-x-2">
      <Checkbox
        id="hasOption"
        checked={hasOption}
        onCheckedChange={(checked) => setHasOption(checked === true)}
      />
      <Label htmlFor="hasOption">This product has variants/options</Label>
    </div>

    {hasOption && (
      <div className="p-4 border rounded-lg bg-blue-50">
        <p className="text-sm text-blue-800 mb-2">
          <strong>Note:</strong> Variant management will be available after creating the base product.
        </p>
        <p className="text-sm text-blue-700">
          You&apos;ll be able to add options like size, color, etc., and create specific variants with their own pricing and inventory.
        </p>
      </div>
    )}
  </CardContent>
</Card>
{/* weight, height and stuff */}
  <Card>
    <CardHeader>
      <CardTitle>Shipping Information</CardTitle>
      <CardDescription>Physical dimensions and weight for shipping calculations</CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="weight">Weight (kg)</Label>
          <Input
            id="weight"
            name="weight"
            type="number"
            step="0.001"
            min="0"
            placeholder="0.000"
            className={state.fieldErrors?.weight ? "border-red-500" : ""}
          />
          {state.fieldErrors?.weight && (
            <p className="text-sm text-red-600">{state.fieldErrors.weight}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="length">Length (cm)</Label>
          <Input
            id="length"
            name="length"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            className={state.fieldErrors?.length ? "border-red-500" : ""}
          />
          {state.fieldErrors?.length && (
            <p className="text-sm text-red-600">{state.fieldErrors.length}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="width">Width (cm)</Label>
          <Input
            id="width"
            name="width"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            className={state.fieldErrors?.width ? "border-red-500" : ""}
          />
          {state.fieldErrors?.width && (
            <p className="text-sm text-red-600">{state.fieldErrors.width}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="heigth">Height (cm)</Label>
          <Input
            id="heigth"
            name="heigth"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            className={state.fieldErrors?.heigth ? "border-red-500" : ""}
          />
          {state.fieldErrors?.heigth && (
            <p className="text-sm text-red-600">{state.fieldErrors.heigth}</p>
          )}
        </div>
      </div>
    </CardContent>
  </Card>

  {/* meta info */}
  <Card>
  <CardHeader>
    <CardTitle>SEO & Meta Information</CardTitle>
    <CardDescription>Search engine optimization settings</CardDescription>
  </CardHeader>
  <CardContent className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="metaTitle">Meta Title</Label>
      <Input
        id="metaTitle"
        name="metaTitle"
        placeholder="SEO title for search engines"
        className={state.fieldErrors?.metaTitle ? "border-red-500" : ""}
      />
      {state.fieldErrors?.metaTitle && (
        <p className="text-sm text-red-600">{state.fieldErrors.metaTitle}</p>
      )}
    </div>

    <div className="space-y-2">
      <Label htmlFor="metaDescription">Meta Description</Label>
      <Textarea
        id="metaDescription"
        name="metaDescription"
        rows={3}
        placeholder="Brief description for search engine results (150-160 characters recommended)"
        className={state.fieldErrors?.metaDescription ? "border-red-500" : ""}
      />
      {state.fieldErrors?.metaDescription && (
        <p className="text-sm text-red-600">{state.fieldErrors.metaDescription}</p>
      )}
    </div>
  </CardContent>
</Card>

{/* form actions  */}
                  <div className="flex justify-end space-x-4">
    <Button
      type="button"
      variant="outline"
      onClick={() => router.push('/admin/products')}
    >
      Cancel
    </Button>
    <Button type="submit" disabled={isPending}>
      {isPending ? "Creating Product..." : "Create Product"}
    </Button>
  </div>
</form>
           
              
    );
}