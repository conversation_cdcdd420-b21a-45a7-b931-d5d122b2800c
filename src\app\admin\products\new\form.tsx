"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useActionState, useState } from "react";
import { createProduct, type ActionState } from "@/actions/products/create-product";
import { useRouter } from "next/navigation";
import { Stepper } from 'react-form-stepper';
import { ChevronLeft, ChevronRight } from "lucide-react";

// modularized step components
import BasicInformationStep from "@/components/admin-product-crud/products/create-product-form-components/basic-information";
import PricingAndStockStep from "@/components/admin-product-crud/products/create-product-form-components/pricing-and-stock";
import CategoriesAndTagsStep from "@/components/admin-product-crud/products/create-product-form-components/categories-and-tags";
import ProductOptionsStep from "@/components/admin-product-crud/products/create-product-form-components/product-options-checkbox";
import ProductShipmentInfoStep from "@/components/admin-product-crud/products/create-product-form-components/product-shipment-info";
import MetaInfoStep from "@/components/admin-product-crud/products/create-product-form-components/meta-info";


interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CreateProductFormProps {
  categories: Category[];
}

const steps = [
  { label: 'Basic Info' },
  { label: 'Pricing' },
  { label: 'Categories' },
  { label: 'Options' },
  { label: 'Shipping' },
  { label: 'SEO' },
];

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(createProduct, {
    message: "",
    error: "",
    fieldErrors: {},
  } as ActionState);

  // stepping state
  const [currentStep, setCurrentStep] = useState(0);

  // interactive elements state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasOption, setHasOption] = useState(false);

  // functions to handle interactive elements
  const handleAddTag = () => {
  if (tagInput.trim() && !tags.includes(tagInput.trim())) {
    setTags([...tags, tagInput.trim()]);
    setTagInput("");
  }
};

const handleRemoveTag = (tagToRemove: string) => {
  setTags(tags.filter(tag => tag !== tagToRemove));
};
 const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // step navigation
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <BasicInformationStep fieldErrors={state.fieldErrors} />;
      case 1:
        return (
          <PricingAndStockStep
            fieldErrors={state.fieldErrors}
            isActive={isActive}
            setIsActive={setIsActive}
          />
        );
      case 2:
        return (
          <CategoriesAndTagsStep
            fieldErrors={state.fieldErrors}
            categories={categories}
            selectedCategories={selectedCategories}
            setSelectedCategories={setSelectedCategories}
            tags={tags}
            tagInput={tagInput}
            setTagInput={setTagInput}
            handleAddTag={handleAddTag}
            handleRemoveTag={handleRemoveTag}
            handleKeyDown={handleKeyDown}
          />
        );
      case 3:
        return (
          <ProductOptionsStep
            hasOption={hasOption}
            setHasOption={setHasOption}
          />
        );
      case 4:
        return <ProductShipmentInfoStep fieldErrors={state.fieldErrors} />;
      case 5:
        return <MetaInfoStep fieldErrors={state.fieldErrors} />;
      default:
        return <BasicInformationStep fieldErrors={state.fieldErrors} />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">

      <div className="mb-8">
        <Stepper
          steps={steps}
          activeStep={currentStep}
          styleConfig={{
            activeBgColor: '#3b82f6',
            completedBgColor: '#10b981',
            inactiveBgColor: '#e5e7eb',
            activeTextColor: '#ffffff',
            completedTextColor: '#ffffff',
            inactiveTextColor: '#6b7280',
            size: '2em',
            circleFontSize: '1rem',
            labelFontSize: '0.875rem',
            borderRadius: '50%',
            fontWeight: 500,
          }}
        />
      </div>

      <form action={formAction} className="space-y-6">
        {/* Hidden inputs */}
        <input type="hidden" name="categoryIds" value={JSON.stringify(selectedCategories)} />
        <input type="hidden" name="tags" value={JSON.stringify(tags)} />
        <input type="hidden" name="isActive" value={isActive.toString()} />
        <input type="hidden" name="hasOption" value={hasOption.toString()} />

        {/* Error and Success Messages */}
        {state.message && (
          <div className="rounded-md border border-green-300 bg-green-50 p-4">
            <p className="text-green-800">{state.message}</p>
          </div>
        )}

        {state.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4">
            <p className="text-red-800">{state.error}</p>
          </div>
        )}

        {Array.isArray(state.warnings) && state.warnings.length > 0 && (
          <div className="rounded-md border border-yellow-300 bg-yellow-50 p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">Warnings</h4>
            <ul className="list-disc list-inside text-yellow-700 text-sm space-y-1">
              {state.warnings.map((warning, i) => (
                <li key={i}>{warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Naviga */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/products')}
            >
              Cancel
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button type="submit" disabled={isPending}>
                {isPending ? "Creating Product..." : "Create Product"}
              </Button>
            ) : (
              <Button type="button" onClick={nextStep}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
