import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

const categories = [
  {
    name: "Turbo Systems",
    slug: "turbo-systems",
    description: "Turbochargers and related components"
  },
  {
    name: "Engine Tuning",
    slug: "engine-tuning",
    description: "Engine performance and tuning parts"
  },
  {
    name: "Exhaust Systems",
    slug: "exhaust-systems", 
    description: "Exhaust pipes, mufflers, and performance exhaust"
  },
  {
    name: "Air Intake",
    slug: "air-intake",
    description: "Air filters, intake systems, and intercoolers"
  },
  {
    name: "Electronics",
    slug: "electronics",
    description: "ECU tuning, sensors, and electronic components"
  },
  {
    name: "Suspension",
    slug: "suspension",
    description: "Coilovers, springs, and suspension components"
  },
  {
    name: "Brakes",
    slug: "brakes",
    description: "Brake pads, discs, and performance braking systems"
  },
  {
    name: "Accessories",
    slug: "accessories",
    description: "General automotive accessories and tools"
  }
];

async function seedCategories() {
  console.log("Seeding categories...");
  
  for (const category of categories) {
    const existing = await prisma.category.findUnique({
      where: { slug: category.slug }
    });
    
    if (!existing) {
      await prisma.category.create({
        data: category
      });
      console.log(`Created category: ${category.name}`);
    } else {
      console.log(`Category already exists: ${category.name}`);
    }
  }
  
  console.log("Categories seeding completed!");
}

seedCategories()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
