import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

const optionTypesData = [
  {
    name: "Color",
    description: "Product color options",
    values: ["Black", "White", "Red", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
  },
  {
    name: "<PERSON><PERSON>",
    description: "Product size options",
    values: ["XS", "S", "M", "L", "XL", "XXL"]
  },
  {
    name: "Material",
    description: "Product material options",
    values: ["Aluminum", "Steel", "Carbon Fiber", "Plastic", "Rubber", "Leather"]
  },
  {
    name: "Finish",
    description: "Product finish options",
    values: ["<PERSON><PERSON>", "Glossy", "Brushed", "Polished", "Anodized", "Powder Coated"]
  },
  {
    name: "Diameter",
    description: "Pipe or component diameter",
    values: ["2.5 inch", "3 inch", "3.5 inch", "4 inch", "5 inch", "6 inch"]
  },
  {
    name: "Thread",
    description: "Thread specifications",
    values: ["M8", "M10", "M12", "M14", "M16", "M18", "M20"]
  }
];

async function seedOptionTypes() {
  console.log("Seeding option types...");
  
  for (const optionTypeData of optionTypesData) {
    // Check if option type already exists
    const existingOptionType = await prisma.optionType.findUnique({
      where: { name: optionTypeData.name }
    });
    
    if (!existingOptionType) {
      // Create option type
      const optionType = await prisma.optionType.create({
        data: {
          name: optionTypeData.name,
          description: optionTypeData.description,
        }
      });
      
      console.log(`Created option type: ${optionType.name}`);
      
      // Create option values
      for (const value of optionTypeData.values) {
        await prisma.optionValue.create({
          data: {
            value: value,
            optionTypeId: optionType.id,
          }
        });
      }
      
      console.log(`  Added ${optionTypeData.values.length} values`);
    } else {
      console.log(`Option type already exists: ${optionTypeData.name}`);
    }
  }
  
  console.log("Option types seeding completed!");
}

seedOptionTypes()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
