import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface MetaInfoStepProps {
  fieldErrors?: Record<string, string>;
}

export default function MetaInfoStep({ fieldErrors }: MetaInfoStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO & Meta Information</CardTitle>
        <CardDescription>Search engine optimization settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="metaTitle">Meta Title</Label>
          <Input
            id="metaTitle"
            name="metaTitle"
            placeholder="SEO title for search engines"
            className={fieldErrors?.metaTitle ? "border-red-500" : ""}
          />
          {fieldErrors?.metaTitle && (
            <p className="text-sm text-red-600">{fieldErrors.metaTitle}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="metaDescription">Meta Description</Label>
          <Textarea
            id="metaDescription"
            name="metaDescription"
            rows={3}
            placeholder="Brief description for search engine results (150-160 characters recommended)"
            className={fieldErrors?.metaDescription ? "border-red-500" : ""}
          />
          {fieldErrors?.metaDescription && (
            <p className="text-sm text-red-600">{fieldErrors.metaDescription}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}