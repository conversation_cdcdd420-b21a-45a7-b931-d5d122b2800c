import CreateProductForm from "./form";
import { prisma } from "@/lib/database";

export default async function CreateProductPage() {
  // Fetch categories for the form
  const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
      slug: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Create New Product</h1>
        <p className="text-gray-600 mt-2">Add a new product to your store</p>
      </div>
      <CreateProductForm categories={categories} />
    </div>
  )
}