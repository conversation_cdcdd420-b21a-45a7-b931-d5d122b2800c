import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const { sku } = await request.json();
    
    if (!sku) {
      return NextResponse.json({ available: true });
    }

    const existingProduct = await prisma.product.findUnique({
      where: { sku },
      select: { id: true }
    });

    return NextResponse.json({ 
      available: !existingProduct,
      message: existingProduct ? 'This SKU is already taken' : 'SKU is available'
    });
  } catch (error) {
    console.error('Error checking SKU:', error);
    return NextResponse.json({ 
      available: true, 
      message: 'Could not verify SKU availability' 
    }, { status: 500 });
  }
}
