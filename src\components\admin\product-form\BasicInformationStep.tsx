import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface BasicInformationStepProps {
  fieldErrors?: Record<string, string>;
}

export default function BasicInformationStep({ fieldErrors }: BasicInformationStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Information</CardTitle>
        <CardDescription>Essential product details</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Product Name *</Label>
            <Input
              id="name"
              name="name"
              required
              className={fieldErrors?.name ? "border-red-500" : ""}
            />
            {fieldErrors?.name && (
              <p className="text-sm text-red-600">{fieldErrors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">URL Slug *</Label>
            <Input
              id="slug"
              name="slug"
              required
              className={fieldErrors?.slug ? "border-red-500" : ""}
            />
            {fieldErrors?.slug && (
              <p className="text-sm text-red-600">{fieldErrors.slug}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            rows={4}
            placeholder="Detailed product description..."
            className={fieldErrors?.description ? "border-red-500" : ""}
          />
          {fieldErrors?.description && (
            <p className="text-sm text-red-600">{fieldErrors.description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
