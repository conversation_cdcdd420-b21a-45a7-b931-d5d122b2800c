import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const { slug } = await request.json();
    
    if (!slug) {
      return NextResponse.json({ available: true });
    }

    const existingProduct = await prisma.product.findUnique({
      where: { slug },
      select: { id: true }
    });

    return NextResponse.json({ 
      available: !existingProduct,
      message: existingProduct ? 'This slug is already taken' : 'Slug is available'
    });
  } catch (error) {
    console.error('Error checking slug:', error);
    return NextResponse.json({ 
      available: true, 
      message: 'Could not verify slug availability' 
    }, { status: 500 });
  }
}
